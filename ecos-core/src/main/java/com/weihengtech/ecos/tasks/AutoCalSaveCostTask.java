package com.weihengtech.ecos.tasks;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientHomeSaveCostDO;
import com.weihengtech.ecos.model.vos.price.TibberEleVO;
import com.weihengtech.ecos.prometheus.ScheduledTaskMetrics;
import com.weihengtech.ecos.service.app.ClientHomeDeviceService;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.ele.ClientHomeSaveCostService;
import com.weihengtech.ecos.service.ele.impl.ClientElePriceRetailerServiceImpl;
import com.weihengtech.ecos.utils.AsyncResultUtil;
import com.weihengtech.ecos.utils.InitUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 自动下发电价策略定时任务
 *
 * <AUTHOR>
 * @date 2024/10/23 14:39
 * @version 1.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class AutoCalSaveCostTask {

	@Resource
	private ClientHomeDeviceService clientHomeDeviceService;
	@Resource
	private ClientHomeSaveCostService clientHomeSaveCostService;
	@Resource
	private ClientHomeService clientHomeService;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private RedisTemplate<String, List<EleDayAheadPriceDto>> redisTemplate;
	@Resource
	private ScheduledTaskMetrics scheduledTaskMetrics;

	@XxlJob("autoCalSaveCostTask")
	public void autoCalSaveCostTask() {
        try {
            // 查询当前数据中心所有家庭设备数据
            List<ClientHomeDeviceDo> allMasterHomeDevice = clientHomeDeviceService.getAllMasterHomeDevice();
            if (CollUtil.isEmpty(allMasterHomeDevice)) {
                printLog("当前数据中心家庭为空，无需计算");
				return;
            }
            // 按照家庭分组
            Map<Long, List<ClientHomeDeviceDo>> homeGroup = allMasterHomeDevice.stream()
                    .collect(Collectors.groupingBy(ClientHomeDeviceDo::getHomeId));
            // 查询家庭信息
            List<ClientHomeDo> allHomeList = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery().in(
                    ClientHomeDo::getId, homeGroup.keySet()));
            if (CollUtil.isEmpty(allHomeList)) {
                printLog("当前数据中心家庭为空，无需计算");
                return;
            }
            // 家庭分页
            List<List<ClientHomeDo>> partition = Lists.partition(allHomeList, 20);
            // 多线程并发计算
            for (List<ClientHomeDo> homeList : partition) {
                threadPoolTaskExecutor.execute(() -> calHomeCostSaving(homeList, homeGroup));
            }
        } catch (Exception e) {
			scheduledTaskMetrics.recordFailure("autoCalSaveCostTask");
        }
    }

	/** 计算所有家庭节省成本数据 */
	private void calHomeCostSaving(List<ClientHomeDo> homeList, Map<Long, List<ClientHomeDeviceDo>> homeGroup) {
		List<ClientHomeSaveCostDO> allHomeSaveCost = new ArrayList<>();
		for (ClientHomeDo homeInfo : homeList) {
			List<ClientHomeDeviceDo> homeDeviceList = homeGroup.get(homeInfo.getId());
			List<ClientHomeSaveCostDO> saveCostList = clientHomeSaveCostService.costSaving(homeInfo, homeDeviceList, -1);
			if (CollUtil.isEmpty(saveCostList)) {
				printLog(String.format("当前家庭：%s(%s)无电价信息或无用电量信息", homeInfo.getHomeName(), homeInfo.getId()));
				continue;
			}
			allHomeSaveCost.addAll(saveCostList);
			printLog(String.format("当前家庭：%s(%s)已完成成本计算", homeInfo.getHomeName(), homeInfo.getId()));
		}
		// 持久化
		clientHomeSaveCostService.saveBatch(allHomeSaveCost);
		printLog(String.format("已完成%d家庭的成本数据持久化", allHomeSaveCost.size()));
	}


	@XxlJob("loadTibberPriceList")
	public void loadTibberPriceList() {
        try {
            ClientElePriceRetailerServiceImpl retailerService = InitUtil.getBean(ClientElePriceRetailerServiceImpl.class);
            List<ClientElePriceRetailerDO> homePriceInfoList = retailerService.queryAllTypeHomePriceInfo();
            if (CollUtil.isEmpty(homePriceInfoList)) {
                return;
            }
            printLog(String.format("零售商电价家庭共有%d个", homePriceInfoList.size()));
            List<Pair<Long, List<EleDayAheadPriceDto>>> allHomePriceInfo = AsyncResultUtil.multiThreadDone(homePriceInfoList,
                    i -> {
                        List<EleDayAheadPriceDto> priceList = retailerService.queryTibberEle(TibberEleVO.builder()
                                .token(i.getToken())
                                .tibberHomeId(i.getRetailerHomeId())
                                .time(0)
                                .build());
                        return Pair.of(i.getHomeId(), priceList);
                    }, threadPoolTaskExecutor);
            if (CollUtil.isEmpty(allHomePriceInfo)) {
                return;
            }
            for (Pair<Long, List<EleDayAheadPriceDto>> pair : allHomePriceInfo) {
                redisTemplate.opsForValue().set(RedisRefConstants.buildTibberTodayPriceKey(pair.getKey()),
                        pair.getValue(), 24, TimeUnit.HOURS);
                printLog(String.format("已缓存家庭tibber电价：%d", pair.getKey()));
            }
        } catch (Exception e) {
			scheduledTaskMetrics.recordFailure("loadTibberPriceList");
        }
    }

	/** 打印日志 */
	private void printLog(String msg, Exception... e) {
		if (e == null) {
			log.info(msg);
			XxlJobHelper.log(msg);
		}else {
			log.error(msg, e);
			XxlJobHelper.log(msg, e);
		}
	}
}
